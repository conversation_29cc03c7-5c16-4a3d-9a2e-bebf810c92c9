package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.viewmodel.MonitoringDataViewModel
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据展示Screen
 * 用于展示所有监控商品的详细数据和变化记录
 * @param viewModel 监控数据视图模型
 * @param onNavigateBack 返回导航回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringDataScreen(
    viewModel: MonitoringDataViewModel,
    onNavigateBack: () -> Unit
) {
    // 从ViewModel获取状态
    val monitoredProducts by viewModel.monitoredProducts.collectAsState()
    val selectedProduct by viewModel.selectedProduct.collectAsState()
    val selectedProductChangeRecords by viewModel.selectedProductChangeRecords.collectAsState()
    val isLoading by viewModel.isLoading
    val errorMessage by viewModel.errorMessage

    // 从ViewModel获取格式化器
    val dateFormat = viewModel.dateFormat
    val fullDateFormat = viewModel.fullDateFormat

    // 显示错误信息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            // 可以在这里显示Toast或Snackbar
            viewModel.clearErrorMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Monitoring Data") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshData() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 显示商品列表
            ProductListScreen(
                products = monitoredProducts,
                viewModel = viewModel,
                modifier = Modifier.padding(paddingValues)
            )
        }
    }
}

/**
 * 商品列表页面
 */
@Composable
fun ProductListScreen(
    products: List<ProductMonitorEntity>,
    viewModel: MonitoringDataViewModel,
    modifier: Modifier = Modifier
) {
    // 管理每个商品的展开状态
    var expandedProductIds by remember { mutableStateOf(setOf<String>()) }
    // 管理每个商品的变化记录
    var productChangeRecords by remember { mutableStateOf(mapOf<String, List<ProductChangeRecordEntity>>()) }
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(products) { product ->
            ProductListItem(
                product = product,
                isExpanded = expandedProductIds.contains(product.id),
                changeRecords = productChangeRecords[product.id] ?: emptyList(),
                onToggleExpand = { productId ->
                    if (expandedProductIds.contains(productId)) {
                        // 收起时清除变化记录
                        expandedProductIds = expandedProductIds - productId
                        productChangeRecords = productChangeRecords - productId
                    } else {
                        // 展开时加载变化记录
                        expandedProductIds = expandedProductIds + productId
                        // 使用协程获取变化记录
                        coroutineScope.launch {
                            try {
                                // 直接获取一次数据，不需要持续监听
                                val records =
                                    viewModel.getProductChangeRecords(product.id, product.shopId)
                                records.collect { recordList ->
                                    productChangeRecords =
                                        productChangeRecords + (productId to recordList.sortedByDescending { it.changeTime })
                                }
                            } catch (e: Exception) {
                                Log.e(
                                    "ProductListScreen",
                                    "Failed to load change records for product ${product.id}",
                                    e
                                )
                            }
                        }
                    }
                },
                dateFormat = viewModel.dateFormat,
                fullDateFormat = viewModel.fullDateFormat
            )
        }
    }
}

/**
 * 商品列表项组件
 */
@SuppressLint("DefaultLocale")
@Composable
fun ProductListItem(
    product: ProductMonitorEntity,
    isExpanded: Boolean,
    changeRecords: List<ProductChangeRecordEntity>,
    onToggleExpand: (String) -> Unit,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat
) {

    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // 主要信息区域 - 点击展开/收起
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onToggleExpand(product.id) }
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 商品标题
                Text(
                    text = product.title,
                    fontWeight = FontWeight.Medium,
                    maxLines = if (isExpanded) Int.MAX_VALUE else 2,
                    overflow = TextOverflow.Ellipsis
                )

                // 展开/收起图标
                Icon(
                    imageVector = if (isExpanded) Icons.Default.Clear else Icons.Default.KeyboardArrowDown,
                    contentDescription = if (isExpanded) "收起" else "展开",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 基本信息行
            Row(
                modifier = Modifier.fillMaxWidth().padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 价格信息
                if (product.currentPrice > 0) {
                    Row {
                        Text(
                            text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        if (product.marketPrice > 0 && product.marketPrice != product.currentPrice) {
                            Text(
                                text = "¥${String.format("%.2f", product.marketPrice / 100.0)}",
                                fontSize = 10.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // 状态标签
                Surface(
                    color = when {
                        product.available == 1 && product.stockNum > 0 -> Color(0xFF4CAF50) // 绿色 - 有货
                        product.isSeckill == 1 -> Color(0xFFFF9800) // 橙色 - 秒杀
                        product.available == 0 -> Color(0xFF9E9E9E) // 灰色 - 下架
                        product.stockNum == 0 -> Color(0xFFF44336) // 红色 - 缺货
                        else -> Color(0xFF607D8B) // 蓝灰色 - 未知状态
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when {
                            product.available == 1 && product.stockNum > 0 -> "有货"
                            product.isSeckill == 1 -> "秒杀"
                            product.available == 0 -> "下架"
                            product.stockNum == 0 -> "缺货"
                            else -> "未知"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            // 展开的详细信息
            if (isExpanded) {
                Spacer(modifier = Modifier.height(8.dp))

                // 详细信息网格
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 商品ID和SKU
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "商品ID: ${product.id}",
                            fontSize = 11.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.weight(1f)
                        )
                        if (product.originalSkuCode.isNotEmpty()) {
                            Text(
                                text = "SKU: ${product.originalSkuCode}",
                                fontSize = 11.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    // 库存和限购信息
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "库存: ${if (product.stockNum > 0) "${product.stockNum}件" else "未知"}",
                            fontSize = 11.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        if (product.restrictLimit > 0) {
                            Text(
                                text = "限购: ${product.restrictLimit}件",
                                fontSize = 11.sp,
                                color = Color(0xFFFF9800)
                            )
                        }
                    }

                    // 店铺和分类信息
                    if (product.shopId.isNotEmpty() || product.categoryId.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            if (product.shopId.isNotEmpty()) {
                                Text(
                                    text = "店铺ID: ${product.shopId}",
                                    fontSize = 11.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                            if (product.categoryId.isNotEmpty()) {
                                Text(
                                    text = "分类: ${product.categoryId}",
                                    fontSize = 11.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }

                    // 特殊标签
                    if (product.productTags.isNotEmpty() || product.goodsTag.isNotEmpty() || product.priceKind.isNotEmpty()) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            if (product.priceKind.isNotEmpty()) {
                                Surface(
                                    color = Color(0xFF9C27B0).copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(4.dp)
                                ) {
                                    Text(
                                        text = product.priceKind,
                                        fontSize = 9.sp,
                                        color = Color(0xFF9C27B0),
                                        modifier = Modifier.padding(
                                            horizontal = 4.dp,
                                            vertical = 2.dp
                                        )
                                    )
                                }
                            }
                            if (product.goodsTag.isNotEmpty()) {
                                Surface(
                                    color = Color(0xFF607D8B).copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(4.dp)
                                ) {
                                    Text(
                                        text = product.goodsTag,
                                        fontSize = 9.sp,
                                        color = Color(0xFF607D8B),
                                        modifier = Modifier.padding(
                                            horizontal = 4.dp,
                                            vertical = 2.dp
                                        )
                                    )
                                }
                            }
                            if (product.isPerformanceHourHour) {
                                Surface(
                                    color = Color(0xFF4CAF50).copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(4.dp)
                                ) {
                                    Text(
                                        text = "小时达",
                                        fontSize = 9.sp,
                                        color = Color(0xFF4CAF50),
                                        modifier = Modifier.padding(
                                            horizontal = 4.dp,
                                            vertical = 2.dp
                                        )
                                    )
                                }
                            }
                        }
                    }

                    // 配送和备注信息
                    if (product.deliveryTimeDesc.isNotEmpty()) {
                        Text(
                            text = "配送: ${product.deliveryTimeDesc}",
                            fontSize = 11.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    if (product.fewStockRemark.isNotEmpty()) {
                        Text(
                            text = "库存备注: ${product.fewStockRemark}",
                            fontSize = 11.sp,
                            color = Color(0xFFFF9800)
                        )
                    }

                    // 监控状态
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Surface(
                            color = if (product.isMonitoringEnabled) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color(
                                0xFFF44336
                            ).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(4.dp)
                        ) {
                            Text(
                                text = if (product.isMonitoringEnabled) "✓ 监控中" else "✗ 已停止",
                                fontSize = 10.sp,
                                color = if (product.isMonitoringEnabled) Color(0xFF4CAF50) else Color(
                                    0xFFF44336
                                ),
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }

                        // 变化记录数量
                        if (changeRecords.isNotEmpty()) {
                            Surface(
                                color = Color(0xFF2196F3).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(4.dp)
                            ) {
                                Text(
                                    text = "${changeRecords.size}条变化",
                                    fontSize = 10.sp,
                                    color = Color(0xFF2196F3),
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                    }

                    // 变化记录列表
                    if (changeRecords.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "变化记录 (最近${minOf(changeRecords.size, 10)}条)",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        // 显示最近的变化记录（最多10条）
                        changeRecords.take(10).forEach { record ->
                            ChangeRecordCompactItem(
                                record = record,
                                dateFormat = fullDateFormat
                            )
                            Spacer(modifier = Modifier.height(2.dp))
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 更新时间
            Text(
                text = "更新时间: ${dateFormat.format(product.lastUpdateTime)}",
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 紧凑的变化记录项组件
 */
@Composable
fun ChangeRecordCompactItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 变化类型标签
            Surface(
                color = when (record.changeType) {
                    ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                    ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                    ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                    ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                    ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                    else -> Color(0xFF607D8B)
                },
                shape = RoundedCornerShape(4.dp)
            ) {
                Text(
                    text = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> "价格"
                        ProductChangeType.STOCK_CHANGE -> "库存"
                        ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                        ProductChangeType.INFO_CHANGE -> "信息"
                        ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                        else -> "其他"
                    },
                    color = Color.White,
                    fontSize = 8.sp,
                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 1.dp)
                )
            }

            Spacer(modifier = Modifier.width(6.dp))

            // 变化内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                    Text(
                        text = "${record.oldValue} → ${record.newValue}",
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.primary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                } else if (record.changeDescription.isNotEmpty()) {
                    Text(
                        text = record.changeDescription,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }

        // 时间
        Text(
            text = dateFormat.format(record.changeTime),
            fontSize = 9.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 商品详情页面
 */
@Composable
fun ProductDetailScreen(
    product: ProductMonitorEntity,
    changeRecords: List<ProductChangeRecordEntity>,
    dateFormat: SimpleDateFormat,
    fullDateFormat: SimpleDateFormat,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 返回按钮和标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回"
                )
            }
            Text(
                text = "商品详情",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 商品信息卡片
            item {
                ProductDetailCard(product, dateFormat)
            }

            // 变化记录标题
            item {
                Text(
                    text = "变化记录 (${changeRecords.size})",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                // 调试信息
                android.util.Log.d(
                    "ProductDetailScreen",
                    "Displaying ${changeRecords.size} change records for product ${product.id}"
                )
            }

            // 变化记录列表
            if (changeRecords.isEmpty()) {
                item {
                    Card(
                        colors = cardThemeOverlay(),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "暂无变化记录",
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            } else {
                items(changeRecords) { record ->
                    ChangeRecordItem(record, fullDateFormat)
                }
            }
        }
    }
}

/**
 * 商品详情卡片
 */
@Composable
fun ProductDetailCard(
    product: ProductMonitorEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 商品标题
            Text(
                text = product.title,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            if (product.subtitle.isNotEmpty()) {
                Text(
                    text = product.subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 商品基本信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "商品ID",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = product.id,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                if (product.originalSkuCode.isNotEmpty()) {
                    Column(
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "SKU代码",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = product.originalSkuCode,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 价格和状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "当前价格",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    if (product.currentPrice > 0) {
                        Text(
                            text = "¥${String.format("%.2f", product.currentPrice / 100.0)}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        Text(
                            text = "暂无价格",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 状态标签
                Surface(
                    color = when {
                        product.available == 1 && product.stockNum > 0 -> Color(0xFF4CAF50) // 绿色 - 有货
                        product.isSeckill == 1 -> Color(0xFFFF9800) // 橙色 - 秒杀
                        product.available == 0 -> Color(0xFF9E9E9E) // 灰色 - 下架
                        product.stockNum == 0 -> Color(0xFFF44336) // 红色 - 缺货
                        else -> Color(0xFF607D8B) // 蓝灰色 - 未知状态
                    },
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = when {
                            product.available == 1 && product.stockNum > 0 -> "有货"
                            product.isSeckill == 1 -> "秒杀"
                            product.available == 0 -> "下架"
                            product.stockNum == 0 -> "缺货"
                            else -> "未知"
                        },
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 时间信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "首次添加",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = dateFormat.format(product.firstAddTime),
                        fontSize = 12.sp
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "最后更新",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = dateFormat.format(product.lastUpdateTime),
                        fontSize = 12.sp
                    )
                }
            }

            // 监控状态
            if (product.isMonitoringEnabled) {
                Spacer(modifier = Modifier.height(8.dp))
                Surface(
                    color = Color(0xFF4CAF50).copy(alpha = 0.1f),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "✓ 监控已启用",
                        color = Color(0xFF4CAF50),
                        fontSize = 12.sp,
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 变化记录项
 */
@Composable
fun ChangeRecordItem(
    record: ProductChangeRecordEntity,
    dateFormat: SimpleDateFormat
) {
    Card(
        colors = cardThemeOverlay(),
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.productId,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                Surface(
                    color = when (record.changeType) {
                        ProductChangeType.PRICE_CHANGE -> Color(0xFF2196F3)
                        ProductChangeType.STOCK_CHANGE -> Color(0xFF4CAF50)
                        ProductChangeType.AVAILABILITY_CHANGE -> Color(0xFFFF9800)
                        ProductChangeType.INFO_CHANGE -> Color(0xFF9C27B0)
                        ProductChangeType.SECKILL_STATUS_CHANGE -> Color(0xFFF44336)
                        else -> Color(0xFF607D8B)
                    },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = when (record.changeType) {
                            ProductChangeType.PRICE_CHANGE -> "价格"
                            ProductChangeType.STOCK_CHANGE -> "库存"
                            ProductChangeType.AVAILABILITY_CHANGE -> "可用性"
                            ProductChangeType.INFO_CHANGE -> "信息"
                            ProductChangeType.SECKILL_STATUS_CHANGE -> "秒杀"
                            else -> "其他"
                        },
                        color = Color.White,
                        fontSize = 10.sp,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "字段: ${record.fieldName}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            if (record.oldValue.isNotEmpty() || record.newValue.isNotEmpty()) {
                Text(
                    text = "${record.oldValue} → ${record.newValue}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Text(
                text = dateFormat.format(record.changeTime),
                fontSize = 11.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}


